<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Link Test</title>
</head>
<body>
    <h1>Company Link Processing Test</h1>
    
    <div id="test-results"></div>

    <script>
        // 模拟 processEntityLinksForDisplay 函数的核心逻辑（修复后的版本）
        function processEntityLinksForDisplay(text, entities) {
            console.log('Input text:', text);
            console.log('Input entities:', entities);

            if (!text || !entities || entities.length === 0) {
                return text;
            }

            let processedText = text;

            entities.forEach((entity) => {
                console.log('Processing entity:', entity);

                if (entity.name && entity.id && entity.country) {
                    const linkUrl = `https://bizr.example.com/enterprise#/detail?tid=${entity.id}&name=${encodeURIComponent(entity.name)}&country=${entity.country}`;
                    const linkHtml = ` <a href="${linkUrl}" target="_blank" rel="noopener noreferrer">${entity.name}</a> `;

                    // 转义特殊字符，创建安全的正则表达式
                    const escapedName = entity.name.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
                    console.log('Escaped name:', escapedName);

                    // 使用全局替换，不依赖单词边界，避免Unicode字符问题
                    const regex = new RegExp(escapedName, "g");
                    console.log('Regex:', regex);

                    const beforeReplace = processedText;
                    processedText = processedText.replace(regex, linkHtml.trim());
                    console.log('Before replace:', beforeReplace);
                    console.log('After replace:', processedText);
                }
            });

            return processedText;
        }

        // 旧版本（有问题的版本）用于对比
        function processEntityLinksForDisplayOld(text, entities) {
            if (!text || !entities || entities.length === 0) {
                return text;
            }

            let processedText = text;

            entities.forEach((entity) => {
                if (entity.name && entity.id && entity.country) {
                    const linkUrl = `https://bizr.example.com/enterprise#/detail?tid=${entity.id}&name=${encodeURIComponent(entity.name)}&country=${entity.country}`;
                    const linkHtml = ` <a href="${linkUrl}" target="_blank" rel="noopener noreferrer">${entity.name}</a> `;

                    // 转义特殊字符，创建安全的正则表达式
                    const escapedName = entity.name.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
                    const regex = new RegExp(`\\b${escapedName}\\b`, "g"); // 使用单词边界（有问题）
                    processedText = processedText.replace(regex, linkHtml);
                }
            });

            return processedText;
        }

        // 测试用例
        const testCases = [
            {
                name: "Normal English Company",
                text: "This is about APPLE INC company information.",
                entities: [
                    {
                        name: "APPLE INC",
                        id: "123",
                        country: "US"
                    }
                ]
            },
            {
                name: "Cyrillic Company Name",
                text: "Information about ФИЛИАЛ ООО CHINA RAILWAY ASIA EUROPE CONSTRUCTION INVESTMENT CO LTD В РЕСПУБЛИКЕ КАЗАХСТАН company.",
                entities: [
                    {
                        name: "ФИЛИАЛ ООО CHINA RAILWAY ASIA EUROPE CONSTRUCTION INVESTMENT CO LTD В РЕСПУБЛИКЕ КАЗАХСТАН",
                        id: "456",
                        country: "KZ"
                    }
                ]
            },
            {
                name: "Mixed Characters",
                text: "Company 中国铁路 RAILWAY CO., LTD. is mentioned here.",
                entities: [
                    {
                        name: "中国铁路 RAILWAY CO., LTD.",
                        id: "789",
                        country: "CN"
                    }
                ]
            }
        ];

        // 运行测试
        const resultsDiv = document.getElementById('test-results');
        
        testCases.forEach((testCase, index) => {
            const result = processEntityLinksForDisplay(testCase.text, testCase.entities);
            
            const testDiv = document.createElement('div');
            testDiv.style.marginBottom = '20px';
            testDiv.style.padding = '10px';
            testDiv.style.border = '1px solid #ccc';
            
            const hasLink = result.includes('<a href=');
            const status = hasLink ? '✅ PASS' : '❌ FAIL';
            
            testDiv.innerHTML = `
                <h3>${status} Test ${index + 1}: ${testCase.name}</h3>
                <p><strong>Original:</strong> ${testCase.text}</p>
                <p><strong>Entity:</strong> ${testCase.entities[0].name}</p>
                <p><strong>Result:</strong> ${result}</p>
                <p><strong>Has Link:</strong> ${hasLink}</p>
            `;
            
            resultsDiv.appendChild(testDiv);
        });
    </script>
</body>
</html>
