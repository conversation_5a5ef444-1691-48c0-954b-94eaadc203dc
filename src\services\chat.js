import { AIRequest } from './request';
import { url } from "./hostConfig";
import { message } from "antd";
import { isCN, errorMessage } from "@/utils/index";

// const env = process.env.UMI_ENV || process.env.NODE_ENV || 'development';
// console.log(process.env.API_BASE_URL,"==process.env.API_BASE_URL");
// console.log(env,"env");

// 获取会话列表
export async function getChatList(params) {
  const { offset = 0, limit = 50 } = params;
  const response = await AIRequest("/api/ai/v1/chats", {
    method: "GET",
    params: {
      offset,
      limit,
    },
  });

  return response?.data || {};
}

// 创建新会话
export async function createChat(params) {
  const response = await AIRequest("/api/ai/v1/chats", {
    method: "POST",
    data: params,
  });

  if (response?.code) {
    errorMessage(response?.code)
  }

  return response?.data || {};
}

// 更新会话
export async function updateChat(id, params) {
  const response = await AIRequest(`/api/ai/v1/chats/${id}`, {
    method: "PUT",
    data: params,
  });

  return response?.data || {};
}

// 删除会话
export async function deleteChat(id) {
  const response = await AIRequest(`/api/ai/v1/chats/${id}`, {
    method: "DELETE",
  });

  return response?.data || {};
}

// 获取公司列表
export async function getCompanyList(params = {}) {
  const { page = 0, size = 20 } = params;
  return AIRequest("/api/bizr/v1/internal/user/company", {
    method: "GET",
    params: {
      page,
      size,
    },
  });
}

// 获取会话消息列表
export async function getChatMessages(chatId, params) {
  const { offset = 0, limit = 50 } = params;
  const response = await AIRequest("/api/ai/v1/messages", {
    method: "GET",
    params: {
      chatId,
      offset,
      limit,
    },
  });
  
  return response?.data || {};
}

// 消息反馈接口
export async function messageFeedback(messageId, like) {
  const response = await AIRequest(`/api/ai/v1/messages/${messageId}/feedback`, {
    method: "POST",
    data: {
      like: like,
    },
  });

  return response?.data || {};
}

// 平台反馈接口
export async function platformFeedback(params) {
  const response = await AIRequest(`/api/feedback/v1/feedback`, {
    method: "POST",
    data: params,
  });

  return response;
}

// 消息SSE - 发起请求并判断响应类型
export async function chatService(content, signal, chatId = "default") {
  // 构造新的请求体格式
  const requestBody = {
    chatId,
    content:
      typeof content === "object" && content !== null && content.content
        ? content.content
        : typeof content === "object" && content !== null
          ? content
          : {
              userPrompt: content,
              entities: [],
            },
  };

  // 如果传入的参数包含scene，添加到请求体中
  if (typeof content === "object" && content !== null && content.scene) {
    requestBody.scene = content.scene;
  }

  const response = await fetch(`/api/ai/v1/messages`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestBody),
    signal, // 添加signal以支持取消
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // 检查响应的 Content-Type 来判断是流式响应还是普通 JSON 响应
  const contentType = response.headers.get("content-type");

  if (contentType && contentType.includes("text/event-stream")) {
    // 流式响应，返回 reader
    return {
      type: "stream",
      reader: response.body.getReader(),
    };
  } else {
    // 普通 JSON 响应（错误响应），解析并返回
    const errorData = await response.json();
    return {
      type: "error",
      data: errorData,
    };
  }
}
