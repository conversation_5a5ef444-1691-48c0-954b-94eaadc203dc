import { useState, useCallback, useRef, useEffect } from "react";
import { history } from "umi";
import {
  getChatList,
  createChat,
  deleteChat,
  getChatMessages,
  chatService,
  messageFeedback,
} from "@/services/chat";
import { isCN, errorMessage } from "@/utils/index";
import { processSSEStream } from "@/utils/sseProcessor";

const generateId = () => Math.random().toString(36).substring(2, 11);

const formatTime = (date) => {
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatDate = (date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) {
    return "今天";
  } else if (days === 1) {
    return "昨天";
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString("zh-CN");
  }
};

export const useChat = (conversationIdFromUrl) => {
  const [conversations, setConversations] = useState([]);
  const [activeConversationId, setActiveConversationId] = useState(null);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isLoadingMoreChats, setIsLoadingMoreChats] = useState(false);
  const [hasMoreChats, setHasMoreChats] = useState(true);
  const [chatOffset, setChatOffset] = useState(0);
  // 消息历史加载状态
  const [isLoadingMoreMessages, setIsLoadingMoreMessages] = useState(false);
  // 为每个会话维护独立的流式内容引用
  const streamingContentRefs = useRef(new Map());
  // 为每个会话维护独立的AbortController
  const abortControllerRefs = useRef(new Map());
  const initializedRef = useRef(false);
  // 存储每个会话的生成状态 - 使用 useState 以便触发重新渲染
  const [generatingConversations, setGeneratingConversations] = useState(new Set());
  // 存储每个会话的加载状态 - 使用 useState 以便触发重新渲染
  const [loadingConversations, setLoadingConversations] = useState(new Set());

  const activeConversation = conversations.find(
    (c) => c.id === activeConversationId
  );

  // 计算当前活动会话是否正在生成
  const isGenerating = activeConversationId
    ? generatingConversations.has(activeConversationId)
    : false;

  // 计算当前活动会话是否正在加载
  const isLoading = activeConversationId
    ? loadingConversations.has(activeConversationId)
    : false;

  // 暂停生成
  const pauseGeneration = useCallback(() => {
    if (activeConversationId) {
      const abortController = abortControllerRefs.current.get(activeConversationId);
      if (abortController) {
        abortController.abort();
        abortControllerRefs.current.delete(activeConversationId);
      }

      // 移除当前会话的加载和生成状态
      setLoadingConversations(prev => {
        const newSet = new Set(prev);
        newSet.delete(activeConversationId);
        return newSet;
      });
      setGeneratingConversations(prev => {
        const newSet = new Set(prev);
        newSet.delete(activeConversationId);
        return newSet;
      });

      // 更新最后一条消息的状态
      setConversations((prev) =>
        prev.map((conv) => {
          if (conv.id === activeConversationId) {
            const messages = [...conv.messages];
            const lastMessage = messages[messages.length - 1];
            if (
              lastMessage &&
              (lastMessage.isTyping || lastMessage.isThinking)
            ) {
              messages[messages.length - 1] = {
                ...lastMessage,
                content:
                  lastMessage.content +
                  `\n\n[${isCN ? "你已让系统停止这条回答" : "Output stopped"}]`,
                isTyping: false,
                isThinking: false,
                timestamp: formatTime(new Date()),
              };
              return {
                ...conv,
                messages,
                lastMessage: messages[messages.length - 1].content,
                timestamp: formatDate(new Date()),
              };
            }
          }
          return conv;
        })
      );
    }
  }, [activeConversationId]);

  // 创建新对话 - 只是切换到空会话状态
  const createNewConversation = useCallback(() => {
    // 如果当前已经是空会话状态，不执行任何操作
    if (activeConversationId === null) return;

    // 切换到空会话状态，取消当前会话选中
    setActiveConversationId(null);

    // 更新URL到基础chat路径
    history.push("/chat");
  }, [activeConversationId]);

  // 重新生成回复（重新发送一条新消息）
  const regenerateMessage = useCallback(
    async (messageId) => {
      const currentConversation = conversations.find(
        (c) => c.id === activeConversationId
      );
      if (!currentConversation) return;

      const messageIndex = currentConversation.messages.findIndex(
        (m) => m.id === messageId
      );
      if (messageIndex <= 0) return;

      // 找到对应的用户消息
      let userMessageIndex = -1;
      for (let i = messageIndex - 1; i >= 0; i--) {
        if (currentConversation.messages[i].role === "user") {
          userMessageIndex = i;
          break;
        }
      }
      if (userMessageIndex === -1) return;

      const userMessage = currentConversation.messages[userMessageIndex];

      // 重新发送用户消息，而不是修改现有的AI回复
      // 构造重新发送的消息内容
      let resendContent;
      let customParams = null;

      if (userMessage.entities && userMessage.entities.length > 0) {
        // 如果原消息有entities，重新构造对象格式
        resendContent = {
          userPrompt: userMessage.originalContent || userMessage.content,
          entities: userMessage.entities,
        };
      } else {
        // 普通文本消息
        resendContent = userMessage.originalContent || userMessage.content;
      }

      // 如果原消息有scene，保持一致
      if (userMessage.scene) {
        customParams = { scene: userMessage.scene };
      }

      // 调用sendMessage重新发送
      await sendMessage(resendContent, customParams);
    },
    [activeConversationId, conversations, sendMessage]
  );

  // 处理消息点赞
  const handleLike = useCallback(
    async (messageId) => {
      try {
        // 找到当前消息
        const currentConversation = conversations.find(
          (conv) => conv.id === activeConversationId
        );
        if (!currentConversation) return;

        const message = currentConversation.messages.find(
          (msg) => msg.id === messageId
        );
        if (!message) return;

        // 确定新的点赞状态：如果已经点赞则取消，否则点赞
        const newLikeState = message.like === true ? null : true;

        // 调用API
        await messageFeedback(messageId, newLikeState);

        // 更新本地状态
        setConversations((prev) =>
          prev.map((conv) => {
            if (conv.id === activeConversationId) {
              const messages = conv.messages.map((msg) => {
                if (msg.id === messageId) {
                  return { ...msg, like: newLikeState };
                }
                return msg;
              });
              return { ...conv, messages };
            }
            return conv;
          })
        );
      } catch (error) {
        console.error(error);
      }
    },
    [activeConversationId, conversations]
  );

  // 处理消息点踩
  const handleDislike = useCallback(
    async (messageId) => {
      try {
        // 找到当前消息
        const currentConversation = conversations.find(
          (conv) => conv.id === activeConversationId
        );
        if (!currentConversation) return;

        const message = currentConversation.messages.find(
          (msg) => msg.id === messageId
        );
        if (!message) return;

        // 确定新的点踩状态：如果已经点踩则取消，否则点踩
        const newLikeState = message.like === false ? null : false;

        // 调用API
        await messageFeedback(messageId, newLikeState);

        // 更新本地状态
        setConversations((prev) =>
          prev.map((conv) => {
            if (conv.id === activeConversationId) {
              const messages = conv.messages.map((msg) => {
                if (msg.id === messageId) {
                  return { ...msg, like: newLikeState };
                }
                return msg;
              });
              return { ...conv, messages };
            }
            return conv;
          })
        );
      } catch (error) {
        console.error(error);
      }
    },
    [activeConversationId, conversations]
  );

  // 发送消息
  const sendMessage = useCallback(
    async (content, customParams = null) => {
      let currentConversationId = activeConversationId;

      // 处理消息内容，支持字符串和对象格式
      const isObjectContent = typeof content === "object" && content !== null;
      const displayContent = isObjectContent
        ? isCN
          ? `为我生成一份${content.entities[0]?.name || "企业"}的背景调查报告`
          : `Generate a background report for ${content.entities[0]?.name || "the company"}`
        : content;

      // 对于标题，也使用处理过的内容，而不是原始的userPrompt
      const titleContent = isObjectContent
        ? processEntityReferences(content.userPrompt, content.entities)
        : content;

      // 如果没有活动对话，先调用API创建一个新的会话
      if (!currentConversationId) {
        try {
          const response = await createChat({
            title:
              titleContent.slice(0, 20) +
              (titleContent.length > 20 ? "..." : ""),
          });

          if (response && response.id) {
            // API创建成功，使用返回的ID
            currentConversationId = response.id;

            const newConversation = {
              id: response.id,
              title:
                response.title ||
                titleContent.slice(0, 20) +
                  (titleContent.length > 20 ? "..." : ""),
              lastMessage: "",
              timestamp: formatDate(new Date()),
              messages: [],
              createdDate: new Date().toISOString(),
            };

            setConversations((prev) => [newConversation, ...prev]);
            setActiveConversationId(currentConversationId);
            // 更新URL到新会话
            history.push(`/chat/${currentConversationId}`);
          } else {
            // 创建会话失败，直接退出
            console.error("Failed to create chat: no ID returned");
            return;
          }
        } catch (error) {
          console.error("Error creating chat:", error);
          return;
        }
      }

      // 判断消息场景
      let scene = null;
      if (customParams?.scene) {
        scene = customParams.scene;
      } else if (
        isObjectContent &&
        content.entities &&
        content.entities.length > 0
      ) {
        scene = "COMPANY_INFO"; // 背景调查场景
      }

      const userMessage = {
        id: generateId(),
        content: displayContent,
        role: "user",
        timestamp: formatTime(new Date()),
        scene: scene,
        originalContent: isObjectContent ? content.userPrompt : content,
        entities: isObjectContent ? content.entities : [],
      };

      // 添加用户消息到数组末尾（时间正序）
      setConversations((prev) =>
        prev.map((conv) => {
          if (conv.id === currentConversationId) {
            const updatedMessages = [...conv.messages, userMessage];
            return {
              ...conv,
              messages: updatedMessages,
              lastMessage: displayContent,
              timestamp: formatDate(new Date()),
            };
          }
          return conv;
        })
      );

      // 设置AI响应的scene（与用户消息保持一致）
      const aiScene = scene;

      // 添加当前会话到加载和生成状态
      setLoadingConversations(prev => new Set(prev).add(currentConversationId));
      setGeneratingConversations(prev => new Set(prev).add(currentConversationId));

      // 为当前会话初始化流式内容引用
      streamingContentRefs.current.set(currentConversationId, { thoughts: "", content: "", tips: [] });

      // 为当前会话创建新的AbortController
      const abortController = new AbortController();
      abortControllerRefs.current.set(currentConversationId, abortController);

      // 用于标记是否已添加AI消息
      let aiMessageAdded = false;
      let aiMessageId = null; // 将从SSE流中获取

      let reader = null;
      try {
        // 处理流式响应
        let requestParams;
        if (customParams && customParams.scene) {
          // 如果有scene参数，构造包含scene的请求参数
          requestParams = {
            scene: customParams.scene,
            content:
              typeof content === "string" ? { userPrompt: content } : content,
          };
        } else {
          // 没有scene参数时，直接传递content
          requestParams =
            typeof content === "string" ? { userPrompt: content } : content;
        }

        // 调用 chatService 获取响应
        const serviceResponse = await chatService(
          requestParams, // 传递处理后的参数
          abortController.signal,
          currentConversationId // 添加 chatId 参数
        );


        // 检查响应类型
        if (serviceResponse.type === 'error') {
          // 处理错误响应
          const errorData = serviceResponse.data;
          errorMessage(errorData?.code);

          // 删除刚刚添加的用户消息
          setConversations((prev) =>
            prev.map((conv) => {
              if (conv.id === currentConversationId) {
                const messages = [...conv.messages];
                // 删除最后一条消息（用户刚发送的消息）
                messages.pop();
                return {
                  ...conv,
                  messages,
                  // 如果删除后还有消息，更新lastMessage；否则清空
                  lastMessage: messages.length > 0 ? messages[messages.length - 1].content : "",
                  timestamp: messages.length > 0 ? formatDate(new Date()) : conv.timestamp,
                };
              }
              return conv;
            })
          );

          // 清除生成状态，让 ChatInput 恢复到可发送状态
          setLoadingConversations(prev => {
            const newSet = new Set(prev);
            newSet.delete(currentConversationId);
            return newSet;
          });
          setGeneratingConversations(prev => {
            const newSet = new Set(prev);
            newSet.delete(currentConversationId);
            return newSet;
          });
          // 清理当前会话的AbortController
          abortControllerRefs.current.delete(currentConversationId);

          // 直接返回，不继续处理
          return;
        }

        // 如果是流式响应，处理 SSE 流
        reader = serviceResponse.reader;
        const result = await processSSEStream(
          reader,
          (chunk) => {
            // 处理ID字段
            if (chunk.type === "id") {
              aiMessageId = chunk.content;
            }

            // 在收到ID且第一次收到有效数据时添加AI消息占位符
            if (
              !aiMessageAdded &&
              aiMessageId &&
              (chunk.type === "thought" ||
                chunk.type === "text" ||
                chunk.type === "separator")
            ) {
              const aiMessage = {
                id: aiMessageId,
                content: "",
                thoughts: "",
                role: "assistant",
                timestamp: formatTime(new Date()),
                isTyping: true,
                isThinking: true,
                scene: aiScene,
              };

              setConversations((prev) =>
                prev.map((conv) => {
                  if (conv.id === currentConversationId) {
                    // AI消息添加到数组末尾（在用户消息之后）
                    return {
                      ...conv,
                      messages: [...conv.messages, aiMessage],
                    };
                  }
                  return conv;
                })
              );
              aiMessageAdded = true;
            }

            if (chunk.type === "thought") {
              const streamingContent = streamingContentRefs.current.get(currentConversationId);
              if (streamingContent) {
                streamingContent.thoughts += chunk.content;
              }
            } else if (chunk.type === "text") {
              const streamingContent = streamingContentRefs.current.get(currentConversationId);
              if (streamingContent) {
                streamingContent.content += chunk.content;
              }
            } else if (chunk.type === "tips") {
              const streamingContent = streamingContentRefs.current.get(currentConversationId);
              if (streamingContent) {
                streamingContent.tips = chunk.content;
              }
            } else if (chunk.type === "separator") {
              // 切换到回复阶段
              setConversations((prev) =>
                prev.map((conv) => {
                  if (conv.id === currentConversationId) {
                    const messages = [...conv.messages];
                    const lastMessage = messages[messages.length - 1]; // AI消息是最后一个
                    messages[messages.length - 1] = {
                      ...lastMessage,
                      isThinking: false,
                    };
                    return { ...conv, messages };
                  }
                  return conv;
                })
              );
              return;
            }

            // 更新UI - 只在AI消息已添加的情况下更新
            if (aiMessageAdded) {
              const streamingContent = streamingContentRefs.current.get(currentConversationId);
              if (streamingContent) {
                setConversations((prev) =>
                  prev.map((conv) => {
                    if (conv.id === currentConversationId) {
                      const messages = [...conv.messages];
                      const lastMessage = messages[messages.length - 1]; // AI消息是最后一个
                      messages[messages.length - 1] = {
                        ...lastMessage,
                        thoughts: streamingContent.thoughts,
                        content: streamingContent.content,
                        tips: streamingContent.tips,
                        isTyping: true,
                        isThinking: lastMessage.isThinking,
                      };
                      return {
                        ...conv,
                        messages,
                        lastMessage:
                          streamingContent.content ||
                          streamingContent.thoughts,
                      };
                    }
                    return conv;
                  })
                );
              }
            }
          },
          abortController.signal
        );

        // 完成后更新状态，使用最终的结果数据
        const streamingContent = streamingContentRefs.current.get(currentConversationId);
        setConversations((prev) =>
          prev.map((conv) => {
            if (conv.id === currentConversationId) {
              const messages = [...conv.messages];
              const lastMessage = messages[messages.length - 1]; // AI消息是最后一个

              const finalContent =
                result?.content || (streamingContent?.content || "");
              const finalThoughts =
                result?.thoughts || (streamingContent?.thoughts || "");
              const finalTips =
                result?.tips || (streamingContent?.tips || []);

              messages[messages.length - 1] = {
                ...lastMessage,
                // 使用最终的结果数据，确保内容不会丢失
                thoughts: finalThoughts,
                content: finalContent,
                tips: finalTips,
                isTyping: false,
                isThinking: false,
                timestamp: formatTime(new Date()),
              };
              return {
                ...conv,
                messages,
                lastMessage: finalContent,
                timestamp: formatDate(new Date()),
              };
            }
            return conv;
          })
        );
      } catch (error) {
        // 确保在错误时关闭reader
        if (reader) {
          try {
            await reader.cancel();
          } catch (e) {
            // 忽略reader.cancel()可能产生的错误
          }
        }

        // 如果是取消请求，不显示错误信息
        if (error.name === "AbortError") {
          return;
        }

        console.error(error);

        // 如果有错误且已经添加了AI消息，则移除最后添加的AI消息
        if (aiMessageAdded) {
          setConversations((prev) =>
            prev.map((conv) => {
              if (conv.id === currentConversationId) {
                const messages = [...conv.messages];
                // 移除最后一条AI消息（因为出错了）
                if (
                  messages.length > 0 &&
                  messages[messages.length - 1].role === "assistant"
                ) {
                  messages.pop();
                }
                return {
                  ...conv,
                  messages,
                  timestamp: formatDate(new Date()),
                };
              }
              return conv;
            })
          );
        }
      } finally {
        // 移除当前会话的加载和生成状态
        setLoadingConversations(prev => {
          const newSet = new Set(prev);
          newSet.delete(currentConversationId);
          return newSet;
        });
        setGeneratingConversations(prev => {
          const newSet = new Set(prev);
          newSet.delete(currentConversationId);
          return newSet;
        });
        // 清理当前会话的流式内容和AbortController
        streamingContentRefs.current.delete(currentConversationId);
        abortControllerRefs.current.delete(currentConversationId);
      }
    },
    [activeConversationId, conversations]
  );

  // 处理用户消息中的实体引用
  const processEntityReferences = useCallback((text, entities) => {
    if (!text || !entities || entities.length === 0) {
      return text;
    }

    let processedText = text;

    // 创建实体ID到名称的映射
    const entityMap = {};
    entities.forEach((entity) => {
      entityMap[entity.id] = entity.name;
    });

    // 替换 @@id@@ 格式的引用
    processedText = processedText.replace(/@@(\w+)@@/g, (match, entityId) => {
      const entityName = entityMap[entityId];
      return entityName ? entityName : match; // 如果找到对应实体名称就替换，否则保持原样
    });

    return processedText;
  }, []);

  // 转换消息格式 - 将新的API格式转换为内部格式
  const transformMessage = useCallback(
    (apiMessage) => {
      if (apiMessage.role === "USER") {
        const entities = apiMessage.content.entities || [];
        const processedContent = processEntityReferences(
          apiMessage.content.userPrompt,
          entities
        );

        return {
          id: apiMessage.id,
          role: "user",
          content: processedContent,
          originalContent: apiMessage.content.userPrompt, // 保留原始内容
          entities: entities,
          timestamp: formatTime(new Date()),
        };
      } else if (apiMessage.role === "ASSISTANT") {
        return {
          id: apiMessage.id,
          role: "assistant",
          content: apiMessage.content.content || "",
          thoughts: apiMessage.content.thinkingContent || "",
          tips: apiMessage.content.tips || [],
          like: apiMessage.like,
          timestamp: formatTime(new Date()),
          isTyping: false,
          isThinking: false,
          scene: apiMessage.content.scene || null, // 从API响应中获取scene
        };
      }
      return null;
    },
    [processEntityReferences]
  );

  // 加载会话消息（首次加载）
  const loadConversationMessages = useCallback(
    async (conversationId) => {
      try {
        const limit = 20; // 每次加载的数量

        // 构造请求参数 - 首次加载使用offset=0
        const params = {
          offset: 0,
          limit,
        };

        const response = await getChatMessages(conversationId, params);

        if (response && response.content) {
          const transformedMessages = response.content
            .map(transformMessage)
            .filter(Boolean) // 过滤掉null值
            .reverse(); // 在这里处理反转，将mock返回的[最新, ..., 最早]转换为[最早, ..., 最新]

          // 更新会话的消息列表和分页状态
          setConversations((prev) =>
            prev.map((conv) => {
              if (conv.id === conversationId) {
                return {
                  ...conv,
                  messages: transformedMessages, // 直接替换
                  hasMoreMessages: response.total > transformedMessages.length, // 根据总数判断是否还有更多
                };
              }
              return conv;
            })
          );

          return transformedMessages;
        }
      } catch (error) {
        console.error(error);
        return [];
      }
    },
    [transformMessage]
  );

  // 加载更多历史消息
  const loadMoreMessages = useCallback(
    async (conversationId) => {
      const conversation = conversations.find((c) => c.id === conversationId);
      if (
        !conversation ||
        isLoadingMoreMessages ||
        !conversation.hasMoreMessages
      ) {
        return;
      }

      try {
        setIsLoadingMoreMessages(true);

        const limit = 20;
        const currentMessageCount = conversation.messages.length;
        const params = {
          offset: currentMessageCount, // 使用当前已加载的消息数量作为offset
          limit,
        };

        const response = await getChatMessages(conversationId, params);

        if (response && response.content) {
          const transformedMessages = response.content
            .map(transformMessage)
            .filter(Boolean)
            .reverse(); // 在这里处理反转，将mock返回的[相对较新, ..., 相对较早]转换为[相对较早, ..., 相对较新]

          // 将新的历史消息添加到现有消息的前面（历史消息更早，应该在前面）
          setConversations((prev) =>
            prev.map((conv) => {
              if (conv.id === conversationId) {
                return {
                  ...conv,
                  messages: [...transformedMessages, ...conv.messages], // 历史消息添加到前面
                  hasMoreMessages: transformedMessages.length === limit, // 如果返回的消息数少于limit，说明没有更多了
                };
              }
              return conv;
            })
          );

          return transformedMessages;
        }
      } catch (error) {
        console.error(error);
        return [];
      } finally {
        setIsLoadingMoreMessages(false);
      }
    },
    [conversations, isLoadingMoreMessages, transformMessage]
  );

  // 选择对话
  const selectConversation = useCallback(
    (conversationId) => {
      // 如果切换到不同的会话，取消之前会话的SSE连接
      if (activeConversationId && activeConversationId !== conversationId) {
        const prevAbortController = abortControllerRefs.current.get(activeConversationId);
        if (prevAbortController) {
          prevAbortController.abort();
          abortControllerRefs.current.delete(activeConversationId);
        }

        // 清理之前会话的生成状态
        setLoadingConversations(prev => {
          const newSet = new Set(prev);
          newSet.delete(activeConversationId);
          return newSet;
        });
        setGeneratingConversations(prev => {
          const newSet = new Set(prev);
          newSet.delete(activeConversationId);
          return newSet;
        });

        // 清理之前会话的流式内容
        streamingContentRefs.current.delete(activeConversationId);
      }

      setActiveConversationId(conversationId);

      // 更新URL路径
      if (conversationId) {
        history.push(`/chat/${conversationId}`);
      } else {
        history.push("/chat");
      }

      // 检查是否需要加载消息
      const conversation = conversations.find((c) => c.id === conversationId);
      if (
        conversation &&
        (!conversation.messages || conversation.messages.length === 0)
      ) {
        // 如果消息列表为空或不存在，加载消息
        loadConversationMessages(conversationId);
      }
    },
    [conversations, loadConversationMessages, activeConversationId]
  );

  // 删除对话
  const deleteConversation = useCallback(
    async (conversationId) => {
      try {
        // 调用删除API
        await deleteChat(conversationId);

        // 从本地状态中移除会话
        setConversations((prev) =>
          prev.filter((conv) => conv.id !== conversationId)
        );

        // 如果删除的是当前活动会话，需要选择新的活动会话
        if (activeConversationId === conversationId) {
          setConversations((prev) => {
            const remainingConversations = prev.filter(
              (conv) => conv.id !== conversationId
            );
            if (remainingConversations.length > 0) {
              // 选择第一个剩余的会话作为活动会话
              const newActiveId = remainingConversations[0].id;
              setActiveConversationId(newActiveId);
              history.push(`/chat/${newActiveId}`);
            } else {
              // 没有剩余会话，清空活动会话
              setActiveConversationId(null);
              history.push("/chat");
            }
            return remainingConversations;
          });
        }

        return true;
      } catch (error) {
        console.error(error);
        // 可以在这里添加错误提示
        return false;
      }
    },
    [activeConversationId]
  );

  // 初始化聊天 - 加载会话列表，使用 useRef 避免重复调用
  const initializeChat = useCallback(() => {
    // 防止重复初始化
    if (initializedRef.current || isLoadingChats) return;

    initializedRef.current = true;
    setIsLoadingChats(true);

    getChatList({ offset: 0, limit: 50 })
      .then((response) => {
        if (response && response.content) {
          const newChats = response.content.map((chat) => ({
            id: chat.id,
            title: chat.title,
            lastMessage: chat.lastMessage || "",
            timestamp: formatDate(new Date(chat.createdDate)),
            messages: [],
            createdDate: chat.createdDate,
          }));

          setConversations(newChats);
          setChatOffset(50);
          setHasMoreChats(newChats.length === 50);

          // 不再自动选择第一个会话，保持 activeConversationId 为 null
        }
      })
      .catch((error) => {
        console.error(error);
        // 出错时重置初始化状态，允许重试
        initializedRef.current = false;
      })
      .finally(() => {
        setIsLoadingChats(false);
      });
  }, []); // 空依赖数组，只在组件挂载时调用一次

  // 加载更多聊天记录
  const loadMoreChats = useCallback(async () => {
    if (isLoadingMoreChats || !hasMoreChats) {
      return;
    }

    setIsLoadingMoreChats(true);

    try {
      const response = await getChatList({ offset: chatOffset, limit: 50 });

      if (response && response.content) {
        const newChats = response.content.map((chat) => ({
          id: chat.id,
          title: chat.title,
          lastMessage: chat.lastMessage || "",
          timestamp: formatDate(new Date(chat.createdDate)),
          messages: [],
          createdDate: chat.createdDate,
        }));

        setConversations((prev) => [...prev, ...newChats]);
        setChatOffset((prev) => prev + 50);
        setHasMoreChats(newChats.length === 50);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoadingMoreChats(false);
    }
  }, [chatOffset, hasMoreChats, isLoadingMoreChats]);

  // 处理URL参数的初始化逻辑
  useEffect(() => {
    // 只有在会话列表加载完成后才处理URL参数
    if (isLoadingChats || conversations.length === 0) {
      return;
    }

    // 如果URL中有会话ID，尝试选中该会话
    if (conversationIdFromUrl) {
      const targetConversation = conversations.find(
        (c) => c.id === conversationIdFromUrl
      );
      if (
        targetConversation &&
        activeConversationId !== conversationIdFromUrl
      ) {
        setActiveConversationId(conversationIdFromUrl);

        // 检查是否需要加载消息
        if (
          !targetConversation.messages ||
          targetConversation.messages.length === 0
        ) {
          loadConversationMessages(conversationIdFromUrl);
        }
      } else if (!targetConversation) {
        // 如果URL中的会话ID不存在，重定向到基础chat路径

        history.push("/chat");
      }
    } else {
      // 如果URL中没有会话ID，确保当前没有选中任何会话
      if (activeConversationId !== null) {
        setActiveConversationId(null);
      }
    }
  }, [
    conversationIdFromUrl,
    conversations,
    isLoadingChats,
    activeConversationId,
    loadConversationMessages,
  ]);

  return {
    conversations,
    activeConversation,
    activeConversationId,
    isLoading,
    isGenerating,
    isLoadingChats,
    isLoadingMoreChats,
    hasMoreChats,
    isLoadingMoreMessages,
    sendMessage,
    regenerateMessage,
    selectConversation,
    createNewConversation,
    deleteConversation,
    loadConversationMessages,
    loadMoreMessages,
    loadMoreChats,
    initializeChat,
    pauseGeneration,
    handleLike,
    handleDislike,
  };
};
